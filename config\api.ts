import { getCurrentLanguage } from "@/lib/i18n";
import Constants from "expo-constants";
import { z } from "zod";

const getEnvVariables = () => {
  const schema = z
    .object({
      API_URL: z.string().url(),
      INTERNAL_API_KEY: z.string(),
    })
    .safeParse({
      API_URL: process.env.EXPO_PUBLIC_API_URL,
      INTERNAL_API_KEY: Constants.expoConfig?.extra?.INTERNAL_API_KEY,
    });

  if (!schema.success) {
    throw new Error("API_URL or INTERNAL_API_KEY is not defined");
  }

  return schema.data;
};

export type ApiResponse<T> = {
  data: T;
  errors?: string[];
};

export const fetchApi = async <T>(
  endpoint: string,
  options: RequestInit = {}
) => {
  const { API_URL, INTERNAL_API_KEY } = getEnvVariables();
  const url = `${API_URL}${
    endpoint.startsWith("/") ? endpoint : `/${endpoint}`
  }`;

  const defaultOptions: RequestInit = {
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
      "X-API-Key": INTERNAL_API_KEY,
      "Accept-Language": await getCurrentLanguage(),
      ...options.headers,
    },
  };

  const response = await fetch(url, {
    ...defaultOptions,
    ...options,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw {
      response: {
        status: response.status,
        data: errorData,
      },
    };
  }

  return response.json() as Promise<ApiResponse<T>>;
};

export const SITE_URL =
  getEnvVariables().API_URL.replace("/api", "") || "https://zimbora.ao";

export const API_URLS = {
  events: "events",
  eventsCategories: "events/categories",
  eventsLocations: "events/locations",
  eventsOrganizers: "events/organizers",
  eventsFavorites: "events/favorites",
  eventById: (id: string) => `events/${id}` as const,
  users: "users",
  experiences: "experiences",
  experiencesFavorites: "experiences/favorites",
  experienceById: (id: string) => `experiences/${id}` as const,
} as const;

type ValueOf<T> = T[keyof T];

type ExtractReturnType<T> = T extends (...args: any[]) => infer R ? R : T;

export type ApiUrlValuesType = ExtractReturnType<ValueOf<typeof API_URLS>>;
