import React, { Fragment } from "react";
import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Platform,
} from "react-native";
import { EventActivityType } from "@/features/events/model";
import { Feather } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import Gap from "../../shared/components/Gap";
import Button from "../../shared/components/Button";
import { useTranslation } from "react-i18next";
import dayjs from "dayjs";

const ActivityCard = ({ activity }: { activity: EventActivityType }) => {
  const formatTime = (dateTime: string) => {
    return dayjs(dateTime).format("HH:mm");
  };

  const formatDate = (dateTime: string) => {
    return dayjs(dateTime).format("DD/MMMM");
  };

  return (
    <View
      style={{
        borderLeftColor: globalStyles.rgba().primary1,
        borderLeftWidth: 1,
        paddingLeft: globalStyles.gap["2xs"],
        paddingBottom: 5,
      }}
    >
      <Text style={styles.activityDescription}>{activity.description}</Text>
      <View style={styles.activityInfo}>
        <View style={styles.infoRow}>
          <Feather
            name="clock"
            size={16}
            color={globalStyles.rgba().light.secondary}
          />
          <Gap x={globalStyles.gap["2xs"]} />
          <Text style={styles.infoText}>
            {formatTime(activity.startTime)}
            {activity.endTime && ` - ${formatTime(activity.endTime)}`}
            {"  •  "}
            <Text style={styles.dateText}>
              {formatDate(activity.startTime)}
            </Text>
          </Text>
        </View>

        {activity.location && (
          <>
            <Gap y={globalStyles.gap["2xs"]} />
            <View style={styles.infoRow}>
              <Feather
                name="map-pin"
                size={16}
                color={globalStyles.rgba().light.secondary}
              />
              <Gap x={globalStyles.gap["2xs"]} />
              <Text style={styles.infoText}>{activity.location}</Text>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

type Props = {
  eventName: string;
  activities: EventActivityType[];
  isOpen: boolean;
  onClose: () => void;
};

const ActivitiesModal = ({ eventName, activities, isOpen, onClose }: Props) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isOpen}
      onRequestClose={onClose}
      presentationStyle={Platform.OS === "ios" ? "pageSheet" : "overFullScreen"}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.header}>
            <Text style={styles.title}>{t("event.activities")}</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <Feather
                name="x"
                size={24}
                color={globalStyles.rgba().dark.secondary}
              />
            </TouchableOpacity>
          </View>

          <Text style={styles.eventName}>{eventName}</Text>
          <Gap y={globalStyles.gap.xs} />

          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            {activities.length > 0 ? (
              activities.map((activity, index) => (
                <Fragment key={index}>
                  <ActivityCard activity={activity} />
                  {index < activities.length - 1 && (
                    <Gap y={globalStyles.gap.xs} />
                  )}
                </Fragment>
              ))
            ) : (
              <Text style={styles.noActivitiesText}>
                {t("event.no_activities_available")}
              </Text>
            )}
            <Gap y={globalStyles.gap.lg} />
          </ScrollView>

          <Button
            text={t("common.back")}
            onPress={onClose}
            type="secondary"
            style={styles.backButton}
          />
        </View>
      </View>
      {Platform.OS !== "ios" && <View style={styles.backdrop} />}
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "flex-end",
    alignItems: "center",
  },
  modalView: {
    backgroundColor: "white",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: globalStyles.gap.sm,
    paddingTop: globalStyles.gap.xs,
    width: "100%",
    height: "85%",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
    flexDirection: "column",
  },
  backdrop: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: -1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  title: {
    fontSize: globalStyles.size["3xl"],
    fontWeight: "bold",
    color: globalStyles.rgba().dark.secondary,
  },
  closeButton: {
    padding: globalStyles.gap["2xs"],
  },
  eventName: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.rgba().dark.primary,
    fontWeight: "600",
    borderBottomWidth: 1,
    borderBottomColor: globalStyles.rgba({ opacity: 0.5 }).light.secondary,
    paddingBottom: globalStyles.gap["xs"],
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: globalStyles.gap.sm,
    minHeight: 200,
  },
  noActivitiesText: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.dark.secondary,
    textAlign: "center",
  },
  activityDescription: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.dark.primary,
    fontWeight: "500",
  },
  activityInfo: {
    marginTop: globalStyles.gap["2xs"],
  },
  infoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  infoText: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.dark.secondary,
    flex: 1,
  },
  dateText: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.tertiary2,
  },
  backButton: {
    marginTop: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
  },
});

export default ActivitiesModal;
