import { cn } from "@/lib/utils";
import { Ref } from "react";
import { TextInput, TextInputProps, View } from "react-native";

type Props = TextInputProps & {
  required?: boolean;
  className?: string;
  inputClassName?: string;
  ref?: Ref<TextInput>;
};

const InputField = ({
  style,
  className,
  multiline = false,
  inputClassName,
  ...props
}: Props) => {
  return (
    <View
      className={cn(
        "relative flex-row rounded-xs bg-light-primary px-5",
        className,
      )}
    >
      {props.required && (
        <View className="absolute left-0 top-0 z-10 size-2 rounded-full bg-primary-2 text-lg" />
      )}
      <TextInput
        {...props}
        multiline={multiline}
        textAlignVertical="center"
        className={cn(
          "ios:-mt-2 ios:mb-1 h-12 flex-1 items-center text-base text-primary-1",
          inputClassName,
        )}
      />
    </View>
  );
};

export default InputField;
