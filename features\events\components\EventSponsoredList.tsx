import { ActivityIndicator, View } from "react-native";
import { MyEventType } from "@/features/events/model";
import globalStyles from "@/lib/globalStyles";
import { RenderIf } from "../../shared/components/RenderIf";
import EventAdCard from "./EventAdCard";
import EventSponsoredCard from "./EventSponsoredCard";

const EventSponsoredList = ({
  events,
  isLoading,
}: {
  events: MyEventType[];
  isLoading: boolean;
}) => {
  const event = events[0];

  return (
    <>
      <RenderIf isTrue={!isLoading}>
        {event ? <EventSponsoredCard event={event} /> : <EventAdCard />}
      </RenderIf>

      <RenderIf isTrue={isLoading}>
        <View
          style={{
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <ActivityIndicator
            size="large"
            color={globalStyles.colors.primary2}
          />
        </View>
      </RenderIf>
    </>
  );
};

export default EventSponsoredList;
