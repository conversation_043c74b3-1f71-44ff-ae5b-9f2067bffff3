import React from "react";
import {
  Modal,
  Platform,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useTranslation } from "react-i18next";
import { Feather } from "@expo/vector-icons";
import { cn } from "@/lib/utils";
import Button from "@/features/shared/components/Button";
import { ExperienceItemType } from "@/features/experience/model";

type Props = {
  experienceName: string;
  items: ExperienceItemType[];
  isOpen: boolean;
  onClose: () => void;
};

const ItemCard = ({ item }: { item: ExperienceItemType }) => {
  return (
    <View className={cn("border-l border-primary-1 pl-2 pb-1")}>
      <Text className={cn("text-lg text-dark-primary font-medium")}>
        {item.name}
      </Text>
      <Text className={cn("text-md text-light-secondary mt-1")}>
        {item.description}
      </Text>
    </View>
  );
};

const ItemsModal = ({ experienceName, items, isOpen, onClose }: Props) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="slide"
      transparent
      visible={isOpen}
      onRequestClose={onClose}
      presentationStyle={Platform.OS === "ios" ? "pageSheet" : "overFullScreen"}
    >
      <View className={cn("flex-1 justify-end items-center")}>
        <View
          className={cn(
            "bg-white gap-6 w-full h-[85%] rounded-t-2xl p-sm shadow-md flex-col"
          )}
        >
          <View className="flex-col">
            <View className={cn("flex-row items-center justify-between")}>
              <Text className={cn("text-3xl font-bold text-dark-secondary")}>
                {t("experience.items")}
              </Text>
              <TouchableOpacity onPress={onClose} className={cn("p-2xs")}>
                <Feather
                  name="x"
                  className={cn("text-2xl text-light-secondary")}
                />
              </TouchableOpacity>
            </View>

            <Text
              className={cn(
                "text-lg text-dark-primary font-semibold border-b border-light-secondary pb-4"
              )}
            >
              {experienceName}
            </Text>
          </View>

          <ScrollView
            className={cn("flex-1")}
            contentContainerStyle={{
              flexGrow: 1,
              paddingBottom: 16,
              minHeight: 200,
            }}
            showsVerticalScrollIndicator={false}
          >
            {items && items.length > 0 ? (
              items.map((item, index) => (
                <View key={`${item.name}-${index}`}>
                  <ItemCard item={item} />
                  {index < items.length - 1 && <View className={cn("h-xs")} />}
                </View>
              ))
            ) : (
              <Text className={cn("text-md text-dark-secondary text-center")}>
                {t("experience.no_items_available")}
              </Text>
            )}
          </ScrollView>

          <Button
            text={t("common.back")}
            onPress={onClose}
            type="secondary"
            className={cn("mt-xs py-2xs")}
          />
        </View>
      </View>
      {Platform.OS !== "ios" && (
        <View className={cn("bg-black/50 absolute inset-0 -z-10")} />
      )}
    </Modal>
  );
};

export default ItemsModal;
