import React from "react";
import {
  View,
  ScrollView,
  Text,
  Pressable,
  StyleProp,
  ViewStyle,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import globalStyles from "@/lib/globalStyles";
// import { ANGOLA_PROVINCES } from "@/lib/angola-provinces";
import { ExperienceFilterType } from "../useExperienceFilterStore";

interface FilterChip {
  key: string;
  label: string;
  value: string;
  onRemove: () => void;
}

interface ActiveFiltersDisplayProps {
  filters: ExperienceFilterType[];
  searchField: ExperienceFilterType;
  onRemoveFilter: (filterKey: ExperienceFilterType["key"]) => void;
  style?: StyleProp<ViewStyle>;
}

const ActiveFiltersDisplay: React.FC<ActiveFiltersDisplayProps> = ({
  filters,
  searchField,
  onRemoveFilter,
  style,
}) => {
  const { t } = useTranslation();

  // Convert filters to display chips
  const getFilterChips = (): FilterChip[] => {
    const chips: FilterChip[] = [];

    filters.forEach((filter) => {
      if (!filter.value) return;

      chips.push({
        key: filter.key,
        label: t(filter.labelKey),
        value: filter.valueKey ? t(filter.valueKey) : filter.value,
        onRemove: () => onRemoveFilter(filter.key),
      });
    });

    return chips;
  };

  const filterChips = getFilterChips();

  // Don't render if no active filters
  if (filterChips.length === 0) {
    return null;
  }

  return (
    <View style={style}>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: 4,
          gap: globalStyles.gap["2xs"],
        }}
        className="flex-grow-0"
      >
        {filterChips.map((chip) => (
          <FilterChipComponent key={chip.key} chip={chip} />
        ))}
      </ScrollView>
    </View>
  );
};

// Individual filter chip component
interface FilterChipComponentProps {
  chip: FilterChip;
}

const FilterChipComponent: React.FC<FilterChipComponentProps> = ({ chip }) => {
  return (
    <View
      className={cn("flex-row items-center rounded-full bg-light-primary ")}
    >
      <Text
        className="text-light-primary bg-light-secondary  rounded-l-full px-2 py-1 text-sm"
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        {chip.label}
      </Text>
      <Text
        className="text-light-secondary text-sm pl-2"
        numberOfLines={1}
        ellipsizeMode="tail"
      >
        {chip.value?.length > 30
          ? chip.value.substring(0, 30) + "..."
          : chip.value}
      </Text>
      <Pressable
        onPress={chip.onRemove}
        className={cn(
          "items-center overflow-hidden px-2 rounded-r-full justify-center"
        )}
        android_ripple={{
          borderless: false,
          color: globalStyles.colors.light.primary,
          foreground: true,
        }}
      >
        <Ionicons name="close" className="text-primary-1 text-base" />
      </Pressable>
    </View>
  );
};

export default ActiveFiltersDisplay;
