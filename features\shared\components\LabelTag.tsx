import globalStyles from "@/lib/globalStyles";
import { Text, View, ViewStyle } from "react-native";

const LabelTag = ({ style, text }: { style?: ViewStyle; text: string }) => {
  return (
    <View
      style={[
        {
          backgroundColor: globalStyles.colors.secondary1,
          borderRadius: globalStyles.rounded.full,
          paddingHorizontal: 5,
        },
        style,
      ]}
    >
      <Text
        style={{
          fontSize: globalStyles.size.sm,
          color: globalStyles.colors.white,
        }}
      >
        {text}
      </Text>
    </View>
  );
};

export default LabelTag;
