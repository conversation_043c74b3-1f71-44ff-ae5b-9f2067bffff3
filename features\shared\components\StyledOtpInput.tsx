import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { OtpInput } from "react-native-otp-entry";
import globalStyles from "@/lib/globalStyles";

type StyledOtpInputProps = {
  setCode?: (code: string) => void;
  numberOfDigits?: number;
  onSubmit: (text: string) => void;
};

export default function StyledOtpInput({
  setCode,
  numberOfDigits = 6,
  onSubmit,
}: StyledOtpInputProps) {
  return (
    <View style={styles.otpContainer}>
      <Text style={styles.otpMessage}>
        Verifique seu e-mail e insira o código de {numberOfDigits} dígitos
      </Text>
      <OtpInput
        numberOfDigits={numberOfDigits}
        onTextChange={(text) => setCode?.(text)}
        onFilled={onSubmit}
        focusColor={globalStyles.rgba().primary1} // Example focus color
        theme={{
          containerStyle: styles.otpInputContainer,
          pinCodeContainerStyle: styles.pinCodeContainer,
          pinCodeTextStyle: styles.pinCodeText,
          focusStickStyle: styles.focusStick,
          focusedPinCodeContainerStyle: styles.focusedPinCodeContainer,
          filledPinCodeContainerStyle: styles.focusedPinCodeContainer,
        }}
        textInputProps={{
          keyboardType: "number-pad",
          textContentType: "oneTimeCode", // Enable autofill
        }}
        type="numeric"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  otpContainer: {
    width: "100%",
    alignItems: "center", // Center items horizontally
    marginBottom: globalStyles.gap.xs,
  },
  otpMessage: {
    color: globalStyles.rgba().dark.secondary,
    marginBottom: globalStyles.gap.xs,
    textAlign: "center",
  },
  otpInputContainer: {
    // Adjust width if needed, default might be 100%
    // width: 'auto', // Example if default width causes issues with gap
  },
  pinCodeContainer: {
    width: 40,
    height: 50,
    borderWidth: 1,
    borderColor: globalStyles.rgba().dark.secondary,
    borderRadius: 8,
    backgroundColor: globalStyles.rgba().light.primary,
    marginHorizontal: globalStyles.size.sm / 2, // Replicate gap using margin
  },

  pinCodeText: {
    fontSize: 18,
    color: globalStyles.rgba().primary1,
  },
  focusStick: {
    // color: globalStyles.rgba().primary1,
    backgroundColor: globalStyles.rgba({ opacity: 0.5 }).primary1,
  },
  focusedPinCodeContainer: {
    borderColor: globalStyles.rgba().primary1,
  },
});
