import { View, Text, StyleSheet } from "react-native";
import React from "react";
import globalStyles from "@/lib/globalStyles";
import { formatDateTime } from "@/lib/format";
import Gap from "@/features/shared/components/Gap";
import { MyEventType } from "@/features/events/model";
import { useTranslation } from "react-i18next";

const EventDetailsTimeline = ({ event }: { event: MyEventType }) => {
  const { t } = useTranslation();

  return (
    <View>
      <Text style={styles.title}>{t("event.timeline")}</Text>
      <Gap y={globalStyles.gap["2xs"]} />
      <View style={styles.timeLabel}>
        <Text style={styles.timeLabelText}>{t("event.opening")}</Text>
        <Text style={styles.timeLabelDate}>
          {formatDateTime(event.startAt)}
        </Text>
      </View>
      <Gap y={globalStyles.gap["2xs"]} />
      <View style={styles.timeLabel}>
        <Text style={styles.timeLabelText}>{t("event.ending")}</Text>
        <Text style={styles.timeLabelDate}>{formatDateTime(event.endAt)}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  title: {
    fontSize: globalStyles.size["2xl"],
    fontWeight: "bold",
    color: globalStyles.colors.tertiary2,
  },
  description: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.tertiary2,
  },
  timeLabel: {
    flexDirection: "row",
    // justifyContent: "space-between",
    alignItems: "center",
  },
  timeLabelText: {
    color: globalStyles.colors.tertiary2,
    fontSize: globalStyles.size.lg,
    fontWeight: "bold",
    marginRight: globalStyles.gap["2xs"],
  },
  timeLabelDate: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.tertiary2,
  },
});

export default EventDetailsTimeline;
