import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet } from "react-native";
import { useTranslation } from "react-i18next";

import Layout from "@/features/shared/components/Layout";
import InputSelect from "@/features/shared/components/InputSelect";
import globalStyles from "@/lib/globalStyles";
import { changeAppLanguage, SupportedLanguages } from "@/lib/i18n";
import i18n from "@/lib/i18n/config";

const styles = StyleSheet.create({
  container: {
    paddingTop: globalStyles.size.pageTop,
    flexDirection: "column",
  },
  sectionTitle: {
    fontSize: globalStyles.size.xl,
    fontWeight: "bold",
    color: globalStyles.colors.dark.secondary,
    marginBottom: globalStyles.gap.sm,
  },
  settingItem: {
    flexDirection: "row",
    gap: globalStyles.gap.xs,
    alignItems: "baseline",
    justifyContent: "space-between",
  },
  settingLabel: {
    flex: 1,
    fontSize: globalStyles.size.xl,
    color: globalStyles.colors.dark.secondary,
    fontWeight: "semibold",
  },
});

type LanguageOption = {
  name: string;
  value: SupportedLanguages;
};

export default function SettingsScreen() {
  const { t } = useTranslation();
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguages>(
    i18n.language as SupportedLanguages
  );

  const languageOptions: LanguageOption[] = [
    { name: t("settings.english"), value: "en" },
    { name: t("settings.portuguese"), value: "pt" },
  ];

  const handleLanguageChange = async (selectedLanguage: LanguageOption) => {
    try {
      setCurrentLanguage(selectedLanguage.value);
      await changeAppLanguage(selectedLanguage.value);
    } catch (error) {
      console.error("Error changing language:", error);
      setCurrentLanguage(i18n.language as SupportedLanguages);
    }
  };

  useEffect(() => {
    const handleLanguageChanged = (lng: string) => {
      setCurrentLanguage(lng as SupportedLanguages);
    };

    i18n.on("languageChanged", handleLanguageChanged);

    return () => {
      i18n.off("languageChanged", handleLanguageChanged);
    };
  }, []);

  const selectedLanguageOption = languageOptions.find(
    (option) => option.value === currentLanguage
  );

  return (
    <Layout title={{ text: t("settings.title") }} noScroll>
      <View style={styles.container}>
        <View style={styles.settingItem}>
          <Text style={styles.settingLabel}>{t("settings.language")}</Text>
          <InputSelect
            items={languageOptions}
            selected={selectedLanguageOption}
            onSelectItem={handleLanguageChange}
            alignment="right"
            dropdownClassName="bg-light-primary"
            className="flex-1"
          />
        </View>
      </View>
    </Layout>
  );
}
