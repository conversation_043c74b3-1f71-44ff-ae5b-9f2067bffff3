import { MyEventType } from "@/features/events/model";
import globalStyles from "@/lib/globalStyles";
import React, { useMemo } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import Button from "../../shared/components/Button";
import useEventFilterStore from "@/features/events/useEventFilterStore";
import { MyEventCategory } from "@/features/categories/model";
import MyDatePicker from "../../shared/components/MyDatePicker";
import InputSelect from "../../shared/components/InputSelect";
import { useTranslation } from "react-i18next";

type Props = {
  events: MyEventType[];
  isLoading: boolean;
  onConclude: () => void;
};

const FilterModalAction = ({
  onClean,
  onConclude,
  totalResults,
  isLoading,
}: {
  onClean: () => void;
  onConclude: () => void;
  totalResults: number;
  isLoading: boolean;
}) => {
  const { t } = useTranslation();

  return (
    <View
      style={{
        gap: globalStyles.gap["2xs"],
        width: "100%",
        alignItems: "flex-end",
        marginTop: globalStyles.size.sm,
      }}
    >
      <Button
        // size='sm'
        text={t("search.see_all_n_results", { totalResults })}
        onPress={onConclude}
        style={{ width: "100%" }}
        disabled={totalResults === 0}
        isLoading={isLoading}
      />
      <TouchableOpacity onPress={() => onClean()}>
        <Text
          style={{
            color: globalStyles.colors.primary1,
            textDecorationLine: "underline",
          }}
        >
          {t("search.remove_filters")}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

const FilterEventsSection = ({ events, isLoading, onConclude }: Props) => {
  const {
    clearFilter,
    selectedStartDate,
    selectedOrganizer,
    selectedLocation,
    selectedCategory,
    setSelectedOrganizer,
    setSelectedCategory,
    setStartDate,
    setFilter,
  } = useEventFilterStore();

  const { t } = useTranslation();

  const getMinMaxDate = (events: MyEventType[]) => {
    const timestamps: number[] = [];
    for (const e of events) {
      const d = e?.startAt ? new Date(e.startAt as any) : null;
      const t = d?.getTime();
      if (typeof t === "number" && !Number.isNaN(t)) timestamps.push(t);
    }
    if (timestamps.length === 0)
      return { minDate: undefined, maxDate: undefined };
    return {
      minDate: new Date(Math.min(...timestamps)),
      maxDate: new Date(Math.max(...timestamps)),
    };
  };

  const getEventsCategories = (events: MyEventType[]) => {
    const mergedCategories = events.reduce((acc, event) => {
      const eventCategories = event.categories || [];
      const newCategories = eventCategories.filter(
        (cat) => !acc.find((c) => c._id === cat._id)
      );
      return [...acc, ...newCategories];
    }, [] as MyEventCategory[]);

    return mergedCategories.reduce((acc, cat) => {
      if (!acc.some((c) => c._id === cat._id)) {
        return [...acc, cat];
      }
      return acc;
    }, [] as MyEventCategory[]);
  };

  const getEventsOrganizers = (events: MyEventType[]) => {
    const set = new Set<string>();
    for (const e of events) {
      if (e?.organizer) set.add(e.organizer);
    }
    return Array.from(set);
  };

  const getEventsLocations = (events: MyEventType[]) => {
    const set = new Set<string>();
    for (const e of events) {
      const locationName = e.locationObj?.name ?? e.location;
      if (locationName) set.add(locationName);
    }
    return Array.from(set);
  };

  const categories = useMemo(() => getEventsCategories(events), [events]);
  const organizers = useMemo(() => getEventsOrganizers(events), [events]);
  const locations = useMemo(() => getEventsLocations(events), [events]);
  const minMaxDate = useMemo(() => getMinMaxDate(events), [events]);

  const totalResults = events?.length || 0;

  const organizersItems = useMemo(
    () =>
      getEventsOrganizers(events)
        .map((v) => ({ name: v, value: v }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, organizers]
  );

  const categoriesItems = useMemo(
    () =>
      getEventsCategories(events)
        .map((c) => ({ name: c.name, value: c._id }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, categories]
  );

  const locationsItems = useMemo(
    () =>
      getEventsLocations(events)
        .map((item) => ({ name: item, value: item }))
        .sort((a, b) => a.name.localeCompare(b.name)),
    [events, locations]
  );

  return (
    <View
      style={{
        gap: 25,
      }}
    >
      <View
        style={{
          gap: globalStyles.gap.xs,
          flexDirection: "row",
          width: "100%",
          alignItems: "flex-start",
        }}
      >
        <InputSelect
          label={t("common.categories")}
          items={categoriesItems}
          selected={
            selectedCategory
              ? { value: selectedCategory._id, name: selectedCategory.name }
              : undefined
          }
          onSelectItem={(item) => {
            setSelectedCategory({ _id: item.value, name: item.name });
          }}
          className="border flex-1 border-primary-1 rounded-full"
        />

        <InputSelect
          label={t("common.organizers")}
          items={organizersItems}
          selected={
            selectedOrganizer
              ? { value: selectedOrganizer, name: selectedOrganizer }
              : undefined
          }
          onSelectItem={(v) => setSelectedOrganizer(v.value)}
          alignment="right"
          className="border flex-1 border-primary-1 rounded-full"
        />
      </View>
      <View
        style={{
          gap: globalStyles.gap.xs,
          flexDirection: "row",
          alignItems: "flex-start",
        }}
      >
        <InputSelect
          label={t("common.locations")}
          items={locationsItems}
          selected={
            selectedLocation
              ? { value: selectedLocation, name: selectedLocation }
              : undefined
          }
          onSelectItem={(v) => setFilter("selectedLocation", v.value)}
          className="border flex-1 border-primary-1 rounded-full"
        />
        <MyDatePicker
          label={t("common.by_date")}
          date={selectedStartDate}
          onChange={(timestamp) => setStartDate(new Date(timestamp))}
          className="flex-1 rounded-full border border-primary-1"
          minimumDate={minMaxDate.minDate}
          maximumDate={minMaxDate.maxDate}
        />
      </View>
      <FilterModalAction
        onClean={clearFilter}
        onConclude={onConclude}
        totalResults={totalResults}
        isLoading={isLoading}
      />
    </View>
  );
};

export default FilterEventsSection;
