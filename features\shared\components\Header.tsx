import { Feather } from "@expo/vector-icons";
import React from "react";
import {
  Pressable,
  StyleProp,
  StyleSheet,
  Text,
  View,
  ViewStyle,
} from "react-native";

import globalStyles from "@/lib/globalStyles";
import { router } from "expo-router";
import ButtonCircle from "./ButtonCircle";
import { RenderIf } from "./RenderIf";

import { Image } from "expo-image";

type Props = {
  title?: {
    text: string;
    onPress?: () => void;
  };
  style?: StyleProp<ViewStyle>;
  backButton?: boolean;
  showSearch?: boolean;
  className?: string;
  children?: React.ReactNode;
};

const Header = ({
  title,
  showSearch,
  style,
  backButton = true,
  className,
  children,
}: Props) => {
  return (
    <>
      <View style={[styles.container, style]}>
        <View style={styles.main}>
          <RenderIf isTrue={backButton}>
            <ButtonCircle onPress={() => router.back()}>
              <Feather
                name="arrow-left"
                size={20}
                color={globalStyles.colors.primary2}
              />
            </ButtonCircle>
          </RenderIf>

          <Text
            onPress={title?.onPress}
            style={title?.onPress ? styles.titleLink : styles.title}
          >
            {title?.text}
          </Text>
          <Pressable
            style={styles.logoContainer}
            onPress={() => router.push("/")}
          >
            <Image
              source={require("@/assets/icons/logo_icon.png")}
              style={styles.logo}
            />
          </Pressable>
        </View>
        {showSearch && children}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    gap: globalStyles.gap.xs,
    width: "100%",
  },
  main: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  title: {
    fontSize: globalStyles.size["2xl"],
    fontWeight: "bold",
    color: globalStyles.colors.dark.secondary,
    maxWidth: "70%",
  },
  titleLink: {
    fontSize: globalStyles.size["2xl"],
    color: globalStyles.colors.primary2,
    fontWeight: "normal",
  },
  logoContainer: {
    width: 36,
    height: 36,
    borderRadius: globalStyles.rounded.full,
  },
  logo: {
    width: 35,
    height: 35,
    borderRadius: globalStyles.rounded.full,
  },
});

export default Header;
