/**
 * Quicksort Algorithm Implementation in JavaScript
 * 
 * Quicksort is a divide-and-conquer algorithm that works by selecting a 'pivot' element
 * from the array and partitioning the other elements into two sub-arrays according to
 * whether they are less than or greater than the pivot.
 * 
 * Time Complexity:
 * - Best/Average Case: O(n log n)
 * - Worst Case: O(n²) - when pivot is always the smallest or largest element
 * 
 * Space Complexity: O(log n) - due to recursive call stack
 */

/**
 * Main quicksort function
 * @param {number[]} arr - Array to be sorted
 * @param {number} low - Starting index (default: 0)
 * @param {number} high - Ending index (default: arr.length - 1)
 * @returns {number[]} - Sorted array
 */
function quicksort(arr, low = 0, high = arr.length - 1) {
    // Base case: if low >= high, the array has 0 or 1 element
    if (low < high) {
        // Partition the array and get the pivot index
        const pivotIndex = partition(arr, low, high);
        
        // Recursively sort elements before and after partition
        quicksort(arr, low, pivotIndex - 1);
        quicksort(arr, pivotIndex + 1, high);
    }
    
    return arr;
}

/**
 * Partition function using Lomuto partition scheme
 * Places the pivot element at its correct position in sorted array
 * and places all smaller elements to left of pivot, and all greater elements to right
 * 
 * @param {number[]} arr - Array to partition
 * @param {number} low - Starting index
 * @param {number} high - Ending index
 * @returns {number} - Index of the pivot element after partitioning
 */
function partition(arr, low, high) {
    // Choose the rightmost element as pivot
    const pivot = arr[high];
    
    // Index of smaller element (indicates right position of pivot found so far)
    let i = low - 1;
    
    for (let j = low; j < high; j++) {
        // If current element is smaller than or equal to pivot
        if (arr[j] <= pivot) {
            i++; // Increment index of smaller element
            swap(arr, i, j);
        }
    }
    
    // Place pivot at correct position
    swap(arr, i + 1, high);
    return i + 1;
}

/**
 * Utility function to swap two elements in an array
 * @param {number[]} arr - Array containing elements to swap
 * @param {number} i - Index of first element
 * @param {number} j - Index of second element
 */
function swap(arr, i, j) {
    const temp = arr[i];
    arr[i] = arr[j];
    arr[j] = temp;
}

/**
 * Alternative quicksort implementation with random pivot selection
 * This helps avoid worst-case performance on already sorted arrays
 * 
 * @param {number[]} arr - Array to be sorted
 * @returns {number[]} - Sorted array
 */
function quicksortRandomPivot(arr) {
    if (arr.length <= 1) {
        return arr;
    }
    
    // Choose random pivot
    const pivotIndex = Math.floor(Math.random() * arr.length);
    const pivot = arr[pivotIndex];
    
    const less = [];
    const equal = [];
    const greater = [];
    
    for (const element of arr) {
        if (element < pivot) {
            less.push(element);
        } else if (element === pivot) {
            equal.push(element);
        } else {
            greater.push(element);
        }
    }
    
    return [
        ...quicksortRandomPivot(less),
        ...equal,
        ...quicksortRandomPivot(greater)
    ];
}

// Example usage and testing
if (typeof module !== 'undefined' && module.exports) {
    // Node.js environment
    module.exports = { quicksort, quicksortRandomPivot, partition, swap };
} else {
    // Browser environment - add to global scope
    window.quicksort = quicksort;
    window.quicksortRandomPivot = quicksortRandomPivot;
}

// Demo usage
console.log('=== Quicksort Demo ===');

// Test case 1: Random array
const testArray1 = [64, 34, 25, 12, 22, 11, 90];
console.log('Original array:', testArray1);
console.log('Sorted array:', quicksort([...testArray1]));

// Test case 2: Already sorted array
const testArray2 = [1, 2, 3, 4, 5];
console.log('Already sorted:', testArray2);
console.log('Quicksort result:', quicksort([...testArray2]));

// Test case 3: Reverse sorted array
const testArray3 = [5, 4, 3, 2, 1];
console.log('Reverse sorted:', testArray3);
console.log('Quicksort result:', quicksort([...testArray3]));

// Test case 4: Array with duplicates
const testArray4 = [3, 6, 8, 10, 1, 2, 1];
console.log('With duplicates:', testArray4);
console.log('Quicksort result:', quicksort([...testArray4]));

// Test case 5: Using random pivot version
const testArray5 = [64, 34, 25, 12, 22, 11, 90];
console.log('Random pivot version:', quicksortRandomPivot([...testArray5]));
