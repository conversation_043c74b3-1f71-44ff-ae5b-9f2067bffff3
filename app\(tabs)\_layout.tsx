import Loading from "@/features/shared/components/Loading";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";
import Feather from "@expo/vector-icons/Feather";
import { Redirect, Tabs } from "expo-router";
import { useTranslation } from "react-i18next";

export default function TabLayout() {
  const { t } = useTranslation();
  const { isLoadingUser, isProfileComplete } = useAppAuth();

  if (!isLoadingUser && !isProfileComplete()) {
    return <Redirect href="/profile-edit" />;
  }

  if (isLoadingUser) {
    return <Loading />;
  }

  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarActiveTintColor: globalStyles.colors.primary2,
        tabBarInactiveTintColor: globalStyles.colors.light.secondary,
        tabBarStyle: {
          paddingBottom: 5,
          // paddingTop: 10,
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          tabBarLabel: t("common.home"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="home" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="search"
        options={{
          tabBarLabel: t("common.events"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="calendar" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="experiences"
        options={{
          tabBarLabel: t("common.experiences"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="compass" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          tabBarLabel: t("common.profile"),
          tabBarIcon: ({ color }) => (
            <Feather size={24} name="user" color={color} />
          ),
        }}
      />
    </Tabs>
  );
}
