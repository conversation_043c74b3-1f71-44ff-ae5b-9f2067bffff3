import { Alert } from "react-native";
import z from "zod";
import * as <PERSON><PERSON> from "@sentry/react-native";

export const handleErrors = ({
  error,
  message,
}: {
  error: any;
  message?: string;
}): string => {
  !__DEV__ && Sentry.captureException(error);
  if (error instanceof z.ZodError || "issues" in error) {
    return z.prettifyError(error);
  }

  return message || "Ocorreu um erro, tente novamente.";
};

export const createHandleErrorDialog = ({
  title,
  btnText,
  message,
  error,
}: {
  error: Error;
  message?: string;
  title: string;
  btnText?: string;
}) => {
  return Alert.alert(title, handleErrors({ error, message }), [
    {
      text: btnText || "OK",
    },
  ]);
};
