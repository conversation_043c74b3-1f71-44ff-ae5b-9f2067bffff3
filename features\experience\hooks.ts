import { API_URLS, fetchApi } from "@/config/api";
import useGetQuery from "@/hooks/useGetQuery";
import { z } from "zod";
import { experienceSchema } from "./model";
import { createHandleErrorDialog, handleErrors } from "@/lib/errors";
import { ExperienceFilterFields } from "./useExperienceFilterStore";
import { mergePathnameQuery } from "@/config/query-utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

export const useGetExperiencesQuery = (query?: ExperienceFilterFields) => {
  const res = useGetQuery({
    apiUrl: API_URLS.experiences,
    queryKey: "experiences",
    enabled: true,
    initialData: [],
    query,
  });

  return { ...res, data: z.array(experienceSchema).parse(res.data) };
};

export const useGetExperienceByIdQuery = ({ id }: { id: string }) => {
  const { t } = useTranslation();
  const res = useGetQuery({
    apiUrl: API_URLS.experienceById(id),
    queryKey: `experience-${id}`,
    enabled: false,
    initialData: undefined,
  });

  if (!res.data && res.isLoading) return { ...res, data: undefined };

  const schema = experienceSchema.safeParse(res.data);

  !schema.success &&
    handleErrors({
      error: schema.error,
      message: t("experience.errors.fetch_experience"),
    });

  return { ...res, data: schema.data };
};

export const useIsExperienceFavorited = (query: {
  userId?: string;
  experienceId?: string;
}) => {
  const { t } = useTranslation();
  const [isFavorited, setIsFavorited] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleFetch = async () => {
    if (!query.userId || !query.experienceId || isLoading) {
      setIsLoading(false);
      return false;
    }

    try {
      setIsLoading(true);
      const url = mergePathnameQuery(API_URLS.experiencesFavorites, {
        ...query,
        type: "check-favorited",
      });
      const res = await fetchApi<{ isFavorited: boolean }>(url, {
        method: "GET",
        cache: "no-store",
      });
      setIsFavorited(res.data.isFavorited);
      return res.data.isFavorited;
    } catch (err) {
      createHandleErrorDialog({
        title: t("experience.errors.check_favorite"),
        error: err as Error,
        message: t("experience.errors.check_favorite_message"),
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    handleFetch();
  }, [query.userId, query.experienceId]);

  return {
    setIsFavorited,
    isFavorited,
    isLoading,
    refetch: handleFetch,
  };
};

export const useToggleExperienceFavorite = ({
  onSuccess,
}: { onSuccess?: () => void } = {}) => {
  const { t } = useTranslation();
  const qClient = useQueryClient();

  const { mutate, isPending: isLoading } = useMutation({
    mutationFn: async (data: { userId: string; experienceId: string }) => {
      const response = await fetchApi<{ isFavorited: boolean }>(
        API_URLS.experiencesFavorites,
        {
          method: "POST",
          body: JSON.stringify({ ...data, type: "toggle-favorite" }),
        }
      );

      return response.data.isFavorited;
    },
    onError: (e) =>
      createHandleErrorDialog({
        title: t("experience.errors.toggle_favorite"),
        message: t("experience.errors.toggle_favorite_message"),
        error: e as any,
      }),

    onSuccess: () => {
      qClient.invalidateQueries({
        queryKey: ["experience-favorited"],
        refetchType: "all",
      });
      onSuccess?.();
    },
  });

  return {
    toggleFavorite: mutate,
    isLoading,
  };
};
