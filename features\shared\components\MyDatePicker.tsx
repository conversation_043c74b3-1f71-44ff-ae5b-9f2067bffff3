import { Ionicons } from "@expo/vector-icons";
import DatePicker from "react-native-date-picker";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import React, { useState, useEffect } from "react";
import { Pressable, StyleSheet, Text, TextInput, View } from "react-native";
import globalStyles from "@/lib/globalStyles";
import { cn } from "@/lib/utils";

dayjs.extend(customParseFormat);

const isValidDate = (date: any): date is Date => {
  return date instanceof Date && !isNaN(date.getTime());
};

const validateDate = (dateValue?: Date | null): Date | null => {
  if (!dateValue) return null;
  return isValidDate(dateValue) ? dateValue : null;
};

const formatDateSafely = (dateValue?: Date | null): string | null => {
  if (!dateValue) return null;
  try {
    if (!isValidDate(dateValue)) return null;

    const dayjsDate = dayjs(dateValue);
    return dayjsDate.isValid() ? dayjsDate.format("DD-MM-YYYY") : null;
  } catch (error) {
    return null;
  }
};

const parseDateFromText = (text: string): Date | null => {
  if (!text || text.length !== 10) return null;
  const parsed = dayjs(text, "DD-MM-YYYY", true);
  return parsed.isValid() ? parsed.toDate() : null;
};

export default function MyDatePicker({
  date,
  label,
  onChange,
  className,
  maximumDate,
  minimumDate,
  defaultValue,
  required = false,
  inputContainerClassName,
  inputClassName,
}: {
  date?: Date;
  label: string;
  onChange: (newDate: Date) => void;
  className?: string;
  maximumDate?: Date;
  minimumDate?: Date;
  defaultValue?: Date;
  required?: boolean;
  inputContainerClassName?: string;
  inputClassName?: string;
}) {
  const validatedDate = validateDate(date);
  const validatedDefaultValue = validateDate(defaultValue);
  const validatedMinimumDate = validateDate(minimumDate);
  const validatedMaximumDate = validateDate(maximumDate);

  const [show, setShow] = useState(false);
  const [textValue, setTextValue] = useState(
    () =>
      formatDateSafely(validatedDate) ||
      formatDateSafely(validatedDefaultValue) ||
      undefined,
  );

  useEffect(() => {
    setTextValue(
      formatDateSafely(validatedDate) ||
        formatDateSafely(validatedDefaultValue) ||
        undefined,
    );
  }, [validatedDate, validatedDefaultValue]);

  const handleDateConfirm = (selectedDate: Date) => {
    onChange(selectedDate);
    setShow(false);
  };

  const handleDateCancel = () => {
    setShow(false);
  };

  const handleTextChange = (text: string) => {
    setTextValue(text);

    if (text.length !== 10) return;
    const parsedDate = parseDateFromText(text);
    if (!parsedDate) return;
    onChange(parsedDate);
  };

  return (
    <View className={cn("relative flex-row rounded-xs", className)}>
      {required && (
        <View className="absolute left-0 top-0 z-10 size-2 rounded-full bg-primary-2" />
      )}
      {validatedDate && <Text style={styles.label}>{label}</Text>}
      <View
        className={cn(
          "flex-1 flex-row items-center gap-2.5 px-5",
          inputContainerClassName,
        )}
      >
        <TextInput
          value={textValue}
          onChangeText={handleTextChange}
          placeholder="Ex: 01-01-2000"
          multiline={false}
          maxLength={10}
          keyboardType="numeric"
          textAlignVertical="center"
          className={cn(
            "ios:-mt-2 ios:mb-1 h-12 flex-1 items-center text-base text-primary-1",
            inputClassName,
          )}
        />
        <Pressable
          className="items-center justify-center overflow-hidden"
          android_ripple={{
            color: globalStyles.rgba().light.primary,
            borderless: false,
            foreground: true,
          }}
          onPress={() => setShow(true)}
        >
          <Ionicons
            name="calendar-outline"
            className="text-2xl text-primary-1"
          />
        </Pressable>
      </View>
      <DatePicker
        modal
        open={show}
        date={validatedDate || validatedDefaultValue || new Date()}
        mode="date"
        locale="pt-BR"
        onConfirm={handleDateConfirm}
        onCancel={handleDateCancel}
        maximumDate={validatedMaximumDate || undefined}
        minimumDate={validatedMinimumDate || undefined}
        theme="auto"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  label: {
    position: "absolute",
    top: -18,
    fontSize: globalStyles.size.sm,
    color: globalStyles.rgba().tertiary2,
  },
});
